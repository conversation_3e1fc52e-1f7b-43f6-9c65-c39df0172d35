# Task: Clean Up SWR Debug Logging Output

## Context
We are working on implementing and debugging a Stale-While-Revalidate (SWR) caching pattern for a React application using Relay GraphQL. The debug logging is currently too noisy even when SWR debug mode is enabled, making it difficult to focus on the actual SWR behavior issues.

## Current Problem
When `window.__enableSWRDebug()` is called and the page is reloaded, the console shows many irrelevant messages that are not related to the SWR debugging task. The user needs to see ONLY the messages that help debug why they see a loading spinner instead of stale cached data when the cache is older than 30 seconds.

## Files to Modify

### 1. `ui/src/relay/debug.ts` (Primary file)
This file contains the debug logging logic with a `swrDebugMode` flag that should filter messages, but it's currently not filtering enough.

Current behavior when `swrDebugMode = true`:
- It checks if messages include certain SWR-related strings
- But many non-SWR messages are still getting through

### 2. `ui/src/relay/useSWRQuery.ts` (Reference only - DO NOT MODIFY)
This file contains the actual SWR implementation and generates the debug messages. You need to understand what messages it generates to properly filter them.

### 3. `ui/src/relay/createPersistedStore.ts` (Reference only - DO NOT MODIFY)
This file handles cache persistence and generates some debug messages that should be filtered out in SWR mode.

## Messages to KEEP in SWR Debug Mode

These are the ONLY messages that should appear when SWR debug mode is enabled:

1. **SWR Mode Status**
   - `🔍 SWR Debug Mode AUTO-ENABLED from previous session`
   - `🔧 Debug settings loaded...` (optional, could be hidden)

2. **Critical SWR Indicators**
   - `🍞 [SWR] Serving STALE data...` - Shows when stale data is being served
   - `🧩 [SWR] Serving PARTIAL data...` - Shows when partial data is served
   - `❌ [SWR] No cached data...` - Shows when there's no cache
   - `⚠️ [SWR] Query has missing fields...` - Shows cache issues

3. **TTL and Cache Status**
   - `⏰ [SWR] TTL Check:` - Shows cache age and freshness
   - `📊 [SWR] Cache availability` - Shows if data is available/stale/missing

4. **Query Execution**
   - `🚀 Starting GraphQL query:` - Shows fetch policy and will revalidate status
   - `🎯 [SWR] Query options` - Shows fetchPolicy, renderPolicy, willSuspend

5. **Background Revalidation**
   - `🔄 [SWR] Scheduling background revalidation`
   - `🚀 [SWR] Starting background revalidation`
   - `📥 [SWR] Background revalidation - data changed`
   - `✨ [SWR] Background revalidation complete - data identical`

## Messages to REMOVE in SWR Debug Mode

All these messages should be filtered out when `swrDebugMode = true`:

1. **Store Hydration Messages**
   - `[Store Hydration]...`
   - `📄 Pagination Analysis:`
   - `[Selective Cache] Schema hash...`
   - `[Bootstrap] Schema hash...`

2. **Persistence Messages**
   - `💾 [Persistence]...`
   - `[Relay Cache] Persisted size:`
   - `observability.ts` messages

3. **Store Analysis**
   - `🏪 Store Analysis`
   - Tables showing record counts

4. **Generic Cache Messages**
   - `⚡ Cache Hit:` 
   - `⚡ Load completed`
   - `✅ Relay pre-check:`

5. **Query Analysis**
   - `📊 Query Analysis:`
   - `🎯 Cache Hit Probability:`
   - Cache prediction messages

6. **Non-SWR Debug Messages**
   - `[initRelayEnvironment]...`
   - `[HelpScout Beacon]...`
   - Component-specific logs like `TimesheetRoster.tsx: Data loaded`
   - `devUtils.ts` messages

7. **Warnings That Aren't Errors**
   - RelayResponseNormalizer warnings (keep console.warn as is)

8. **Duplicate Messages**
   - The same cache key being logged multiple times
   - Redundant TTL checks (keep only unique ones or first occurrence)

## Implementation Approach

In `ui/src/relay/debug.ts`, the `log` function should be updated:

```typescript
// When swrDebugMode is true, be VERY selective
if (swrDebugMode) {
    if (typeof msg === 'string') {
        // Define what we WANT to see
        const criticalSWRMessages = [
            '🍞 [SWR] Serving STALE',
            '🧩 [SWR] Serving PARTIAL',
            '❌ [SWR] No cached data',
            '⚠️ [SWR] Query has missing fields',
            '⏰ [SWR] TTL Check:',
            '📊 [SWR] Cache availability',
            '🚀 Starting GraphQL query:',
            '🎯 [SWR] Query options',
            '🔄 [SWR] Scheduling background',
            '🚀 [SWR] Starting background',
            '📥 [SWR] Background revalidation',
            '✨ [SWR] Background revalidation complete'
        ];
        
        // Check if message matches any critical pattern
        const shouldShow = criticalSWRMessages.some(pattern => msg.includes(pattern));
        
        if (!shouldShow) return;
    }
    console.log(...args);
    return;
}
```

## Additional Considerations

1. **Duplicate Query Executions**: The log shows the same query running 6 times. Consider adding deduplication or showing a count instead of repeating the same message.

2. **Message Grouping**: Consider using `console.group()` to group related messages (e.g., all messages for a single query execution).

3. **Timestamp Simplification**: Instead of full ISO timestamps, consider showing relative times (e.g., "+43s") for better readability.

## Expected Output After Fix

When SWR debug mode is enabled, the console should show something like:

```
🔍 SWR Debug Mode AUTO-ENABLED from previous session
📊 [SWR] Cache availability: status='available' 
⏰ [SWR] TTL Check: Age: 43s, TTL: 30s, Is Fresh: false
🚀 Starting GraphQL query: TimesheetRosterQuery (fetchPolicy='store-only', willRevalidate=true)
🍞 [SWR] Serving STALE data while revalidating in background
🎯 [SWR] Query options: renderPolicy='full', willSuspend=false
🔄 [SWR] Scheduling background revalidation (TTL expired)
🚀 [SWR] Starting background revalidation
📥 [SWR] Background revalidation - data changed, updating store
```

That's it - clean, focused, and only showing what matters for debugging SWR behavior.

## Testing

After implementing the changes:
1. Run `window.__enableSWRDebug()` in console
2. Reload the page
3. Verify only SWR-relevant messages appear
4. Confirm no important SWR messages are filtered out
5. Test with `window.__setDebugVerbosity('normal')` to ensure it shows more details when needed