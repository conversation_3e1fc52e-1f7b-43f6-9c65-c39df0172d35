import { useLazyLoadQuery, GraphQLTaggedNode, useRelayEnvironment, fetchQuery } from 'react-relay';
import type { OperationType, FetchPolicy, RecordProxy } from 'relay-runtime';
import { createOperationDescriptor, getRequest, ConnectionHandler } from 'relay-runtime';
import { relayObservability } from './observability';
import { useEffect, useRef, useState } from 'react';
import { debug, stableStringify, type CacheMissDetails, type CacheMissReason, type QueryAnalysis } from './debug';
import { getRelayEnvironment } from './withPersistence';
import type { PayloadHashTable } from './types/persistence';
import { mergeConnectionEdges } from './cacheFilters';

// Type definitions for improved type safety
type GraphQLVariables = Record<string, unknown>;

interface PaginationVariables extends GraphQLVariables {
    first?: number;
    last?: number;
    after?: string;
    before?: string;
}

interface ConnectionRecord {
    edges?: Record<string, unknown>;
    pageInfo?: {
        hasNextPage?: boolean;
        endCursor?: string;
    };
}

// Type guards for runtime validation
function isPaginatedVariables(vars: GraphQLVariables): vars is PaginationVariables {
    return typeof vars === 'object' && vars !== null && (typeof vars.first === 'number' || typeof vars.last === 'number');
}

function hasEdges(record: unknown): record is ConnectionRecord {
    return typeof record === 'object' && record !== null && 'edges' in record;
}

function isRecordProxy(obj: unknown): obj is RecordProxy {
    return obj !== null && typeof obj === 'object';
}

// SWR Configuration Constants
const REVALIDATE_MS = 30_000; // 30 seconds background revalidation threshold

// PHASE 2: Expanded stable business fields for consistent hash calculation
// This prevents false-positive cache misses due to volatile fields like timestamps
const STABLE_EDGE_FIELDS = [
    // Core identification fields
    'id', 'employeeId', 'employerId', 'userId', 'timesheetId', 'payStubId',
    
    // Business data fields
    'employeeName', 'employerName', 'hours', 'status', 'payPeriod', 'payRate',
    'startDate', 'endDate', 'workDate', 'classification', 'position', 'jobTitle',
    
    // Financial fields
    'grossPay', 'netPay', 'deductions', 'taxes', 'amount', 'rate', 'total',
    
    // Workflow state (stable)
    'approvalStatus', 'processingStatus', 'isActive', 'isEnabled', 'isVisible',
    
    // Metadata (stable)
    'description', 'notes', 'type', 'category', 'priority', 'version'
] as const;

// Global background revalidation tracking (use Map for string keys)
const revalidationTimestamps = new Map<string, number>();

// PHASE 2: Concurrency guard to prevent duplicate in-flight revalidations
const inFlightRevalidations = new Set<string>();

// Global payload hash tracking to avoid no-op publishes
const payloadHashes: PayloadHashTable = {};

// Expose payload hashes globally for debugging
if (typeof window !== 'undefined') {
    window.__RELAY_OP_PAYLOAD_HASHES__ = payloadHashes;
}

/**
 * Simple hash function for payload comparison
 * Uses a basic string hash algorithm for detecting identical payloads
 */
function simpleHash(str: string): string {
    let hash = 0;
    if (str.length === 0) return hash.toString(36);
    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = (hash << 5) - hash + char;
        hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(36);
}

// Type for window relay dev tools and cache metadata
declare global {
    interface Window {
        __RELAY_DEV__?: {
            trackCacheLoad: (startTime: number, fromCache: boolean, queryName?: string) => void;
        };
        __DEBUG_CACHE_DELAY__?: boolean;
        __RELAY_CACHE_HYDRATED_AT__?: number;
        __RELAY_CACHE_SAVED_AT__?: number;
        __RELAY_CACHE_TTL__?: number;
        __RELAY_OP_SAVED_AT__?: Record<string, number>; // NEW: per-operation timestamps
        __RELAY_OP_PAYLOAD_HASHES__?: PayloadHashTable; // NEW: payload hash tracking
    }
}

// Type definitions for Relay internals
interface RelayAvailability {
    status: 'available' | 'missing' | 'stale';
    isStale?: boolean;
    stale?: boolean;
    missing?: Array<{
        path: string;
        field: string;
    }>;
}

// Cache debugging utilities
function extractQueryName(query: GraphQLTaggedNode): string {
    try {
        // Try multiple approaches to get query name
        if ('modern' in query && query.modern && typeof query.modern === 'object') {
            if ('name' in query.modern && query.modern.name) {
                return String(query.modern.name);
            }
            // Try to get from operation property
            if (
                'operation' in query.modern &&
                query.modern.operation &&
                typeof query.modern.operation === 'object' &&
                'name' in query.modern.operation
            ) {
                return String(query.modern.operation.name);
            }
        }

        // Try legacy approach
        if ('params' in query && query.params && typeof query.params === 'object' && 'name' in query.params) {
            return String(query.params.name);
        }

        // Try to extract from query text
        if ('default' in query && query.default && typeof query.default === 'object' && 'params' in query.default) {
            const params = query.default.params as { name?: string };
            if (params.name) {
                return params.name;
            }
        }

        return 'UnknownQuery';
    } catch {
        return 'UnknownQuery';
    }
}

async function analyzeStoreForQuery(queryDoc: GraphQLTaggedNode, queryName: string, variables: GraphQLVariables): Promise<QueryAnalysis> {
    try {
        // Get current Relay store state
        const { getRelayEnvironment } = await import('./withPersistence');
        const environment = await getRelayEnvironment();
        const store = environment.getStore();
        // PHASE 2: Wrap expensive snapshot behind NODE_ENV check for performance
        const storeSnapshot = process.env.NODE_ENV === 'development' ? store.getSource().toJSON() : {};
        // Relay availability check before the query executes
        try {
            const requestNode = getRequest(queryDoc);
            const opDesc = createOperationDescriptor(requestNode, variables);
            const availability = environment.check(opDesc) as RelayAvailability;
            if (availability.status === 'missing') {
                console.group(`🔍 Relay missing paths for ${queryName} (pre-query)`);
                const missing = availability.missing ?? [];
                missing.forEach((path, i: number) => console.log(`${i + 1}.`, path));
                console.groupEnd();
            } else {
                console.log(`✅ Relay pre-check: '${queryName}' is AVAILABLE from cache`);
            }
        } catch (err) {
            console.warn('Relay pre-check failed:', err);
        }

        // Analyze available records
        const allRecords = Object.keys(storeSnapshot);
        const businessRecords = allRecords.filter((id) => {
            const record = storeSnapshot[id] as { __typename?: string };
            return record?.__typename && !id.startsWith('client:');
        });

        // Basic query analysis - this could be enhanced with actual query parsing
        const expectedRecords = [];

        // Try to infer expected records based on query name patterns
        if (queryName.includes('TimeSheet') || queryName.includes('timesheet')) {
            expectedRecords.push(...allRecords.filter((id) => id.includes('TimeSheet')));
        }
        if (queryName.includes('PayStub') || queryName.includes('paystub')) {
            expectedRecords.push(...allRecords.filter((id) => id.includes('PayStub')));
        }
        if (queryName.includes('Employee') || queryName.includes('employee')) {
            expectedRecords.push(...allRecords.filter((id) => id.includes('Employee')));
        }
        if (queryName.includes('User') || queryName.includes('user')) {
            expectedRecords.push(...allRecords.filter((id) => id.includes('User')));
        }

        // If no specific patterns, use business records as baseline
        if (expectedRecords.length === 0) {
            expectedRecords.push(...businessRecords.slice(0, 10)); // Sample of business records
        }

        const availableRecords = expectedRecords.filter((id) => storeSnapshot[id]);
        const missingRecords = expectedRecords.filter((id) => !storeSnapshot[id]);

        const cacheHitProbability = expectedRecords.length > 0 ? availableRecords.length / expectedRecords.length : 0;

        // PHASE 3 FIX: Determine cache miss reason based on actual TTL check, not heuristics
        let reason: CacheMissReason = 'unknown';
        
        // Get actual cache key and timestamp for this query
        const cacheKey = createCacheKey(queryName, variables);
        const opSavedAt = (window.__RELAY_OP_SAVED_AT__ ?? {})[cacheKey];
        const now = Date.now();
        
        if (!opSavedAt) {
            reason = 'forced_network_due_to_missing_timestamp';
        } else if (now - opSavedAt > REVALIDATE_MS) {
            reason = 'ttl_expired';
        } else if (businessRecords.length === 0) {
            reason = 'missing_query_records';
        } else if (availableRecords.length < expectedRecords.length * 0.5) {
            reason = 'incomplete_data';
        } else {
            // Data appears available but network fetch still triggered - investigate further
            reason = 'unknown';
        }

        return {
            queryName,
            expectedRecords,
            availableRecords,
            missingRecords,
            cacheHitProbability,
            reason
        };
    } catch (error) {
        debug.error('Failed to analyze store for query:', error);
        return {
            queryName,
            expectedRecords: [],
            availableRecords: [],
            missingRecords: [],
            cacheHitProbability: 0,
            reason: 'unknown' as CacheMissReason
        };
    }
}

/**
 * Create a cache key for revalidation tracking
 * PHASE 3: Enhanced with logging for key mismatch diagnosis
 */
function createCacheKey(queryName: string, variables: GraphQLVariables): string {
    // Create a stable key from query name and variables using deterministic serialization
    // This ensures the same cache key is generated regardless of object property ordering
    const variablesKey = stableStringify(variables);
    const cacheKey = `${queryName}:${variablesKey}`;
    
    // PHASE 3 FIX: Log variablesKey for key mismatch diagnosis
    if (process.env.NODE_ENV === 'development') {
        debug.log('🔑 [Cache Key] Generated cache key', {
            queryName,
            variablesKey: variablesKey.substring(0, 100) + (variablesKey.length > 100 ? '...' : ''), // Truncate for readability
            fullCacheKey: cacheKey.substring(0, 150) + (cacheKey.length > 150 ? '...' : ''),
            keyLength: cacheKey.length,
            timestamp: new Date().toISOString()
        });
    }
    
    return cacheKey;
}

/**
 * Check if background revalidation should be triggered
 * PHASE 2: Enhanced with concurrency guard to prevent duplicate requests
 */
function shouldRevalidate(cacheKey: string, revalidationCooldown: number = REVALIDATE_MS): boolean {
    // PHASE 2: Check if revalidation is already in flight
    if (inFlightRevalidations.has(cacheKey)) {
        return false; // Don't trigger duplicate revalidation
    }

    const lastRevalidation = revalidationTimestamps.get(cacheKey);
    if (!lastRevalidation) {
        return true; // Never revalidated
    }

    return Date.now() - lastRevalidation > revalidationCooldown;
}

/**
 * Mark that revalidation has been triggered for this cache key
 */
function markRevalidated(cacheKey: string): void {
    revalidationTimestamps.set(cacheKey, Date.now());
}

/**
 * Trigger background revalidation with store-and-network policy
 */
async function triggerBackgroundRevalidation<T extends OperationType>(
    query: GraphQLTaggedNode,
    variables: T['variables'],
    queryName: string,
    cacheKey: string
): Promise<void> {
    // PHASE 2: Mark as in-flight to prevent concurrent revalidations
    if (inFlightRevalidations.has(cacheKey)) {
        if (process.env.NODE_ENV === 'development') {
            debug.log(`🚫 Skipping concurrent revalidation for ${queryName}`, { cacheKey });
        }
        return;
    }

    inFlightRevalidations.add(cacheKey);

    try {
        const environment = await getRelayEnvironment();

        if (process.env.NODE_ENV === 'development') {
            debug.log(`🔄 Starting background revalidation for ${queryName}`, {
                cacheKey,
                timestamp: new Date().toISOString()
            });
        }

        // Perform a network-only revalidation to guarantee a fresh payload; updater will merge or replace edges accordingly
        const requestNode = getRequest(query);
        const networkStart = Date.now();
        if (process.env.NODE_ENV === 'development') {
            debug.log(`🌐 [SWR] Revalidation fetch (network-only) initiated for ${queryName}`, { cacheKey });
        }

        // PHASE 3: Request window alignment to preserve paginated edges
        // Try to detect current cursor position for paginated queries
        let revalidationVariables = variables;

        try {
            // Check if this looks like a paginated query (has 'first' or 'last' and might have 'after'/'before')
            if (isPaginatedVariables(variables)) {
                // Try to find the current end cursor from the store
                const store = environment.getStore();
                const source = store.getSource();
                const rootRecord = source.get('client:root');

                if (rootRecord && isRecordProxy(rootRecord)) {
                    // Look for common connection field patterns in timesheet/roster contexts
                    const connectionFieldPatterns = ['timesheets', 'timeSheets', 'roster', 'employees'];

                    for (const fieldName of connectionFieldPatterns) {
                        try {
                            const connection = ConnectionHandler.getConnection(
                                rootRecord,
                                fieldName,
                                variables // Use original variables for connection lookup
                            );

                            if (connection) {
                                const pageInfo = connection.getLinkedRecord('pageInfo');
                                const endCursor = pageInfo?.getValue('endCursor');

                                if (endCursor && pageInfo?.getValue('hasNextPage')) {
                                    // Use current end cursor to fetch the same window + new data
                                    revalidationVariables = {
                                        ...variables,
                                        after: endCursor,
                                        // Keep the same page size
                                        first: variables.first || 25
                                    };

                                    if (process.env.NODE_ENV === 'development') {
                                        debug.log(`🔄 [SWR] Using cursor alignment for ${fieldName}:`, {
                                            cacheKey,
                                            originalAfter: variables.after,
                                            alignedAfter: endCursor,
                                            pageSize: (revalidationVariables as PaginationVariables).first
                                        });
                                    }
                                    break; // Found a connection, use it
                                }
                            }
                        } catch (connectionError) {
                            // Continue to next field pattern
                        }
                    }
                }
            }
        } catch (cursorError) {
            if (process.env.NODE_ENV === 'development') {
                debug.log('[SWR] Cursor alignment failed, using original variables:', cursorError);
            }
            // Fallback to original variables
            revalidationVariables = variables;
        }

        // PHASE 2: CRITICAL FIX - Capture edge snapshots BEFORE network request
        // This prevents the snapshot from containing the new data, which was the root cause of edge loss
        let existingEdgesSnapshots: Map<string, readonly RecordProxy[]> = new Map();
        let allConnectionFields: string[] = [];
        
        try {
            const store = environment.getStore();
            const sourceBeforeFetch = store.getSource();
            const rootRecord = sourceBeforeFetch.get('client:root');
            
            if (rootRecord) {
                const explicitConnectionFields = ['timesheets', 'timeSheets', 'roster', 'employees'];
                const discoveredConnectionFields: string[] = [];
                
                // Discover connection fields from store
                const allFieldNames = Object.keys(rootRecord);
                allFieldNames.forEach((fieldName: string) => {
                    if (fieldName.includes('__connection') || fieldName.endsWith('Connection')) {
                        const baseFieldName = fieldName.replace(/__connection.*$/, '').replace(/Connection$/, '');
                        if (baseFieldName && !explicitConnectionFields.includes(baseFieldName)) {
                            discoveredConnectionFields.push(baseFieldName);
                        }
                    }
                });
                
                allConnectionFields = [...explicitConnectionFields, ...discoveredConnectionFields];
                
                if (process.env.NODE_ENV === 'development' && discoveredConnectionFields.length > 0) {
                    debug.log(`🔍 [SWR] Discovered additional connection fields:`, discoveredConnectionFields);
                }
                
                // Capture snapshots for each connection field BEFORE fetch using proper Relay traversal
                for (const fieldName of allConnectionFields) {
                    try {
                        // PHASE 2 FIX: Use proper RecordProxy traversal via environment.commitUpdate for consistent access
                        environment.commitUpdate((tempStoreProxy) => {
                            try {
                                const rootProxy = tempStoreProxy.getRoot();
                                const connection = ConnectionHandler.getConnection(rootProxy, fieldName, revalidationVariables);
                                
                                if (connection) {
                                    const existingEdges = connection.getLinkedRecords('edges') || [];
                                    existingEdgesSnapshots.set(fieldName, existingEdges);
                                    
                                    if (process.env.NODE_ENV === 'development') {
                                        debug.log(`📸 [SWR] Captured ${existingEdges.length} existing edges BEFORE fetch for ${fieldName}`, {
                                            cacheKey,
                                            fieldName,
                                            edgeCount: existingEdges.length,
                                            timing: 'before_network_fetch',
                                            method: 'RecordProxy_traversal'
                                        });
                                    }
                                }
                            } catch (proxyError) {
                                if (process.env.NODE_ENV === 'development') {
                                    debug.log(`[SWR] RecordProxy traversal failed for ${fieldName}:`, proxyError);
                                }
                            }
                        });
                    } catch (connectionError) {
                        if (process.env.NODE_ENV === 'development') {
                            debug.log(`[SWR] Failed to capture edges before fetch for ${fieldName}:`, connectionError);
                        }
                    }
                }
            }
        } catch (snapshotError) {
            if (process.env.NODE_ENV === 'development') {
                debug.log('[SWR] Failed to capture edge snapshots before fetch:', snapshotError);
            }
        }

        // PHASE 2: Wrap expensive snapshot behind NODE_ENV check for performance
        const __preStoreSnapshot__ = process.env.NODE_ENV === 'development' ? environment.getStore().getSource().toJSON() : null;

        // Track if network request actually happened
        let networkRequestMade = false;
        
        // Create operation descriptor for the query
        const opDesc = createOperationDescriptor(requestNode, revalidationVariables);
        
        // First, capture the current data for comparison
        const currentSnapshot = environment.lookup(opDesc.fragment);
        const currentData = JSON.stringify(currentSnapshot.data);
        
        // Use environment.execute for manual control over store updates
        // This allows us to fetch without auto-committing to store
        const { Observable } = await import('relay-runtime');
        
        let networkResponse: any = null;
        let fetchError: any = null;
        
        await new Promise<void>((resolve, reject) => {
            const subscription = environment.execute({
                operation: opDesc
            }).subscribe({
                next: (response) => {
                    networkResponse = response;
                },
                error: (error: any) => {
                    fetchError = error;
                    reject(error);
                },
                complete: () => {
                    resolve();
                }
            });
        }).catch(err => {
            if (process.env.NODE_ENV === 'development') {
                debug.error('Network fetch failed:', err);
            }
            throw err;
        });
        
        networkRequestMade = true;
        
        // Compare the network response with current store data
        const networkData = JSON.stringify(networkResponse);
        const dataActuallyChanged = currentData !== networkData;
        
        if (!dataActuallyChanged) {
            if (process.env.NODE_ENV === 'development') {
                debug.log('✨ [SWR] Background revalidation complete - data identical, skipping store update', {
                    cacheKey,
                    queryName,
                    timestamp: new Date().toISOString()
                });
            }
            // Still mark as revalidated to prevent duplicate requests
            markRevalidated(cacheKey);
            
            // Update timestamp since we verified the data is fresh
            const now = Date.now();
            if (window.__RELAY_OP_SAVED_AT__) {
                window.__RELAY_OP_SAVED_AT__[cacheKey] = now;
                
                // Persist the timestamp
                void (async () => {
                    try {
                        const { notifyStoreUpdated } = await import('./createPersistedStore');
                        notifyStoreUpdated();
                    } catch (error) {
                        if (process.env.NODE_ENV === 'development') {
                            debug.error('Failed to persist timestamp:', error);
                        }
                    }
                })();
            }
            
            // Skip store update entirely since nothing changed
            inFlightRevalidations.delete(cacheKey);
            return;
        }
        
        // Data changed - commit it to the store
        if (process.env.NODE_ENV === 'development') {
            debug.log('📥 [SWR] Background revalidation - data changed, updating store', {
                cacheKey,
                queryName,
                timestamp: new Date().toISOString()
            });
        }
        
        // Manually commit the payload since we used execute() instead of fetchQuery()
        environment.commitPayload(opDesc, networkResponse);

        // PHASE 2: CRITICAL FIX - Enhanced post-fetch commitUpdate using pre-captured snapshots
        // This runs AFTER fetchQuery has already updated the store
        // We use this to handle connection edges and detect if data actually changed
        environment.commitUpdate((storeProxy) => {
            try {
                const rootRecordProxy = storeProxy.getRoot();

                // Now process the new edges with the captured snapshots
                for (const fieldName of allConnectionFields) {
                    const connection = ConnectionHandler.getConnection(rootRecordProxy, fieldName, revalidationVariables);
                    if (connection) {
                        const newEdges = connection.getLinkedRecords('edges') || [];
                        const existingEdgesSnapshot = existingEdgesSnapshots.get(fieldName) || [];

                        // --- PHASE 3: Enhanced hash based consistency guard with stable field selection ---
                        const serializeEdges = (edgesArr: readonly RecordProxy[] | null) => {
                            if (!edgesArr) return '[]';
                            return JSON.stringify(
                                edgesArr.map((e) => {
                                    const node = e?.getLinkedRecord('node');

                                    // PHASE 3: Use stable fields only, omit volatile fields like updatedAt, modifiedAt, etc.
                                    const stableFields: Record<string, unknown> = {
                                        id: node?.getValue('id'),
                                        cursor: e?.getValue('cursor')
                                    };

                                    // PHASE 2: Include stable business fields from extracted constant
                                    STABLE_EDGE_FIELDS.forEach((field) => {
                                        const value = node?.getValue(field);
                                        if (value !== undefined && value !== null) {
                                            stableFields[field] = value;
                                        }
                                    });

                                    return stableFields;
                                })
                            );
                        };

                        const prevHash = payloadHashes[cacheKey];
                        const incomingHash = simpleHash(serializeEdges(newEdges));
                        
                        // PHASE 2 FIX: Short-circuit if hash unchanged AND edge count unchanged
                        const dataChanged = prevHash && prevHash !== incomingHash;
                        const edgeCountChanged = existingEdgesSnapshot.length !== newEdges.length;
                        
                        if (!dataChanged && !edgeCountChanged && prevHash) {
                            // PHASE 2 FIX: Complete no-op scenario - skip commitUpdate entirely
                            if (process.env.NODE_ENV === 'development') {
                                debug.log('⚡ [SWR] Skipping commitUpdate - payload and edge count identical', {
                                    cacheKey,
                                    hash: incomingHash,
                                    existingEdgeCount: existingEdgesSnapshot.length,
                                    newEdgeCount: newEdges.length,
                                    skippedMutation: true
                                });
                            }
                            
                            // Update stored hash to maintain consistency
                            payloadHashes[cacheKey] = incomingHash;
                            continue; // Skip to next connection field
                        }

                        if (dataChanged) {
                            // PHASE 2: Data changed – merge with existing edges to preserve pagination slices
                            mergeConnectionEdges(storeProxy as unknown as RecordProxy, connection, newEdges, existingEdgesSnapshot);
                            if (process.env.NODE_ENV === 'development') {
                                debug.log('[SWR] Detected changed payload. Merged with existing edges to preserve pagination.', {
                                    cacheKey,
                                    prevHash,
                                    incomingHash,
                                    existingEdgeCount: existingEdgesSnapshot.length,
                                    newEdgeCount: newEdges.length
                                });
                            }
                        } else {
                            // No data change but edge count changed OR first time – perform merge to preserve pagination
                            mergeConnectionEdges(storeProxy as unknown as RecordProxy, connection, newEdges, existingEdgesSnapshot);
                            if (process.env.NODE_ENV === 'development') {
                                debug.log('[SWR] Payload identical but processing needed – merged edges to retain pagination window.', {
                                    cacheKey,
                                    hash: incomingHash,
                                    existingEdgeCount: existingEdgesSnapshot.length,
                                    newEdgeCount: newEdges.length,
                                    edgeCountChanged
                                });
                            }
                        }

                        // Update stored hash
                        payloadHashes[cacheKey] = incomingHash;
                    }
                }
            } catch (mergeError) {
                if (process.env.NODE_ENV === 'development') {
                    debug.error('[SWR] Edge merge updater failed:', mergeError);
                }
            }
        });

        const networkDuration = Date.now() - networkStart;

        // Variables for tracking store changes (used for network detection)
        let addedRecords = 0;
        let removedRecords = 0;
        let changedRecords = 0;
        let __postStoreSnapshot__: any = null;

        // ---------- DEV ONLY: Diff store snapshots to detect write amplification & edge loss
        if (process.env.NODE_ENV === 'development' && typeof __preStoreSnapshot__ !== 'undefined' && __preStoreSnapshot__) {
            // PHASE 2: Already gated behind NODE_ENV check for performance
            __postStoreSnapshot__ = environment.getStore().getSource().toJSON();

            const preKeys = Object.keys(__preStoreSnapshot__);
            const postKeys = Object.keys(__postStoreSnapshot__);

            addedRecords = postKeys.filter((k) => !(k in __preStoreSnapshot__)).length;
            removedRecords = preKeys.filter((k) => !(k in __postStoreSnapshot__)).length;

            changedRecords = 0;
            for (const key of preKeys) {
                if (
                    key in __postStoreSnapshot__ &&
                    JSON.stringify(__preStoreSnapshot__[key]) !== JSON.stringify(__postStoreSnapshot__[key])
                ) {
                    changedRecords++;
                }
            }

            const countEdges = (snapshot: Record<string, unknown>) =>
                Object.values(snapshot).reduce((sum: number, record) => {
                    if (hasEdges(record) && record.edges) {
                        return sum + Object.keys(record.edges).length;
                    }
                    return sum;
                }, 0);

            const preEdgeCount = countEdges(__preStoreSnapshot__);
            const postEdgeCount = countEdges(__postStoreSnapshot__);

            debug.log('🧮 [SWR] Store diff after revalidation', {
                cacheKey,
                addedRecords,
                removedRecords,
                changedRecords,
                preEdgeCount,
                postEdgeCount,
                edgeDelta: postEdgeCount - preEdgeCount
            });
        }
        // Reliable detection: Check if store actually changed (indicating fresh data)
        let dataChanged = false;
        if (typeof __preStoreSnapshot__ !== 'undefined' && __preStoreSnapshot__ && typeof __postStoreSnapshot__ !== 'undefined') {
            // If records were added or significantly changed, data came from network
            // Small changes might be from optimistic updates or GC
            const significantlyChanged = addedRecords > 0 || changedRecords > 5;
            dataChanged = significantlyChanged;
        }
        
        // Primary signal: Store changes indicate network fetch
        // With force: true and network-only, we expect network fetch
        // Only if NO changes occurred should we doubt the network fetch
        const fromNetwork = networkRequestMade && (dataChanged || addedRecords > 0 || changedRecords > 0);
        
        if (process.env.NODE_ENV === 'development') {
            debug.log(`✅ [SWR] Background fetch outcome for ${queryName}`, {
                cacheKey,
                durationMs: networkDuration,
                source: fromNetwork ? 'network' : 'store',
                dataChanged,
                addedRecords,
                changedRecords
            });
            if (!fromNetwork) {
                debug.log(`⚠️ [SWR] Network request likely skipped because fetchPolicy 'network-only' returned cached data.`, {
                    cacheKey
                });
            }
        }

        // Mark as revalidated to prevent duplicate requests
        markRevalidated(cacheKey);

        // Update per-operation timestamp **only after** network payload arrived
        // CRITICAL: Only update if data actually came from network, not from cache
        const now = Date.now();
        if (fromNetwork && window.__RELAY_OP_SAVED_AT__) {
            window.__RELAY_OP_SAVED_AT__[cacheKey] = now;

            // STEP D: Enhanced development instrumentation for per-operation timestamp writes
            if (process.env.NODE_ENV === 'development') {
                debug.log('📝 Per-op timestamp write', {
                    cacheKey,
                    operation: queryName,
                    timestamp: new Date(now).toISOString(),
                    context: 'background_revalidation'
                });
            }

            // Trigger persistence to save the updated per-operation timestamp
            // Do NOT update global cache timestamp - that should remain as initial load time
            try {
                const { notifyStoreUpdated } = await import('./createPersistedStore');
                notifyStoreUpdated();
            } catch (error) {
                if (process.env.NODE_ENV === 'development') {
                    debug.error('Failed to persist timestamp:', error);
                }
            }
        }

        if (process.env.NODE_ENV === 'development') {
            debug.log(`✅ Background revalidation completed for ${queryName}`, {
                cacheKey,
                timestamp: new Date().toISOString(),
                updatedOpTimestamp: new Date(now).toISOString()
            });
        }

        // Track with observability
        relayObservability.trackCacheHit(); // Background refresh counts as cache efficiency
    } catch (error) {
        if (process.env.NODE_ENV === 'development') {
            debug.error(`❌ Background revalidation failed for ${queryName}:`, error);
        }

        // Don't mark as revalidated on error so it can retry later
        relayObservability.trackCacheMiss();
    } finally {
        // PHASE 2: Always clear in-flight guard to allow future revalidations
        inFlightRevalidations.delete(cacheKey);
    }
}

export function useSWRQuery<T extends OperationType>(query: GraphQLTaggedNode, variables: T['variables']) {
    const trackingRef = useRef(false);
    const startTimeRef = useRef(Date.now());
    const [delayComplete, setDelayComplete] = useState(!window.__DEBUG_CACHE_DELAY__);
    const analysisRef = useRef<QueryAnalysis | null>(null);
    const backgroundRevalidationTriggered = useRef(false);

    // Extract query name for better debugging
    const queryName = extractQueryName(query);

    // Create stable cache key for revalidation tracking
    const cacheKey = createCacheKey(queryName, variables);
    
    // Debug: Track cache key stability
    useEffect(() => {
        if (process.env.NODE_ENV === 'development') {
            debug.log(`🔑 [SWR] Cache key for ${queryName}`, {
                cacheKey,
                variables,
                timestamp: new Date().toISOString()
            });
        }
    }, [cacheKey, queryName, variables]);

    // Perform pre-query analysis for cache debugging
    useEffect(() => {
        if (process.env.NODE_ENV === 'development') {
            void analyzeStoreForQuery(query, queryName, variables)
                .then((analysis) => {
                    analysisRef.current = analysis;
                    debug.queryAnalysis(analysis);
                })
                .catch((error) => {
                    debug.error('Failed to analyze store for query:', error);
                });
        }
    }, [queryName, variables]);

    // Apply debug delay if enabled
    useEffect(() => {
        if (window.__DEBUG_CACHE_DELAY__ && !delayComplete) {
            debug.networkDelay(queryName, 10000);
            debug.log(`🐌 Starting 10-second debug delay for ${queryName} to isolate cache behavior`);

            const timeoutId = setTimeout(() => {
                debug.log(`⏰ Debug delay complete for ${queryName}, proceeding with query execution`);
                setDelayComplete(true);
            }, 10000);

            return () => {
                clearTimeout(timeoutId);
            };
        } else if (!window.__DEBUG_CACHE_DELAY__) {
            // Ensure delay is complete if not enabled
            setDelayComplete(true);
        }
    }, [queryName, delayComplete]);

    // Enhanced SWR fetchPolicy determination with background revalidation
    const environment = useRelayEnvironment();
    let fetchPolicy: FetchPolicy = 'store-or-network';
    let shouldTriggerBackgroundRevalidation = false;
    let availability: RelayAvailability | null = null;

    if (delayComplete) {
        try {
            const requestNode = getRequest(query);
            const opDesc = createOperationDescriptor(requestNode, variables);
            availability = environment.check(opDesc) as RelayAvailability;

            // Use per-operation timestamp for accurate cache age calculation
            const opSavedAt = (window.__RELAY_OP_SAVED_AT__ ?? {})[cacheKey];
            
            // Only use cache hydration time if:
            // 1. No operation-specific timestamp exists AND
            // 2. We're within the TTL window from when cache was hydrated
            // This allows first page load to use cached data without immediate revalidation
            const cacheHydratedAt = (window as any).__RELAY_CACHE_HYDRATED_AT__;
            const cacheSavedAt = (window as any).__RELAY_CACHE_SAVED_AT__; // When cache was originally persisted
            
            let savedAt: number | undefined = opSavedAt;
            if (!savedAt && cacheSavedAt) {
                // Use the original cache save time for TTL calculation on first load
                savedAt = cacheSavedAt;
                if (process.env.NODE_ENV === 'development') {
                    debug.log('📅 Using cache saved time as fallback', {
                        cacheKey,
                        cacheSavedAt: new Date(cacheSavedAt).toISOString(),
                        age: Math.round((Date.now() - cacheSavedAt) / 1000) + 's'
                    });
                }
            }

            // Debug: Log availability status for complex queries
            if (process.env.NODE_ENV === 'development') {
                debug.log(`📊 [SWR] Cache availability for ${queryName}`, {
                    status: availability.status,
                    cacheKey,
                    missingFields: availability.status === 'missing' ? 'Query data not in cache' : 'Data available',
                    timestamp: new Date().toISOString()
                });
            }
            
            // Handle cache availability based on status
            if (availability.status === 'available' || availability.status === 'stale') {
                // Data is fully available in cache
                fetchPolicy = 'store-only';

                // PHASE 1: Simplified TTL logic - respects 30-second TTL across page reloads
                const isCacheFresh = savedAt !== undefined && Date.now() - savedAt <= REVALIDATE_MS;
                shouldTriggerBackgroundRevalidation = !isCacheFresh;
                
                // Log TTL check with accurate timestamp sources
                if (process.env.NODE_ENV === 'development') {
                    const currentTime = Date.now();
                    debug.log('⏰ [TTL Check]', {
                        cacheKey,
                        queryName,
                        hasOpTimestamp: !!opSavedAt,
                        opSavedAt: opSavedAt ? new Date(opSavedAt).toISOString() : 'none',
                        cacheSavedAt: cacheSavedAt ? new Date(cacheSavedAt).toISOString() : 'none',
                        cacheHydratedAt: cacheHydratedAt ? new Date(cacheHydratedAt).toISOString() : 'none',
                        savedAt: savedAt ? new Date(savedAt).toISOString() : 'none',
                        currentTime: new Date(currentTime).toISOString(),
                        ageMs: savedAt ? currentTime - savedAt : 'N/A',
                        ttlMs: REVALIDATE_MS,
                        isCacheFresh,
                        willTriggerRevalidation: shouldTriggerBackgroundRevalidation
                    });
                    
                    if (debug.swrBehavior?.logTTLCheck) {
                        debug.swrBehavior.logTTLCheck({
                            cacheKey,
                            queryName,
                            savedAt,
                            currentTime,
                            ttlMs: REVALIDATE_MS,
                            isCacheFresh,
                            willTriggerRevalidation: shouldTriggerBackgroundRevalidation
                        });
                    }
                }
            } else if (availability.status === 'missing') {
                // CRITICAL FIX: For expensive queries with missing data
                // Check if we have ANY cached data for this query pattern
                const hasAnyCachedData = savedAt !== undefined;
                
                if (hasAnyCachedData) {
                    // We have some historical data - try to show it
                    if (process.env.NODE_ENV === 'development') {
                        debug.log(`⚠️ [SWR] Query has missing fields but attempting partial render`, {
                            queryName,
                            cacheKey,
                            status: 'missing',
                            savedAt: savedAt ? new Date(savedAt).toISOString() : 'none',
                            cacheSavedAt: cacheSavedAt ? new Date(cacheSavedAt).toISOString() : 'none',
                            strategy: 'Will use store-or-network with partial render policy'
                        });
                    }
                    
                    // Use store-or-network but with partial render policy
                    // This shows whatever we have while fetching missing data
                    fetchPolicy = 'store-or-network';
                    // We'll handle render policy below
                } else {
                    // No cached data at all - must fetch from network
                    if (process.env.NODE_ENV === 'development') {
                        debug.log(`❌ [SWR] No cached data for expensive query`, {
                            queryName,
                            cacheKey,
                            status: 'missing',
                            strategy: 'Must fetch from network'
                        });
                    }
                    fetchPolicy = 'store-or-network';
                }
            }
        } catch (err) {
            // Default: fall back to store-or-network behaviour
            if (process.env.NODE_ENV === 'development') {
                debug.error(`Error checking availability for ${queryName}:`, err);
            }
            fetchPolicy = 'store-or-network';
        }
    } else {
        // During artificial debug delay we always force cache
        fetchPolicy = 'store-only';
    }

    // Log the request start with computed policy
    debug.log(`🚀 Starting GraphQL query: ${queryName}`, {
        variables,
        timestamp: new Date().toISOString(),
        fetchPolicy,
        debugDelay: window.__DEBUG_CACHE_DELAY__ ? '10s' : 'disabled',
        willRevalidate: shouldTriggerBackgroundRevalidation
    });

    // Determine render policy based on cache status
    // 'full' = Don't suspend if all data is in cache
    // 'partial' = Show what we have, suspend for missing parts
    const useFullRenderPolicy = fetchPolicy === 'store-only';
    const usePartialRenderPolicy = fetchPolicy === 'store-or-network' && availability?.status === 'missing';

    // Debug: Log when we're about to serve stale/partial data
    if (process.env.NODE_ENV === 'development') {
        if (fetchPolicy === 'store-only' && shouldTriggerBackgroundRevalidation) {
            debug.log(`🍞 [SWR] Serving STALE data for ${queryName} while revalidating in background`, {
                cacheKey,
                fetchPolicy,
                renderPolicy: 'full',
                willRevalidate: true,
                timestamp: new Date().toISOString()
            });
        } else if (usePartialRenderPolicy) {
            debug.log(`🧩 [SWR] Serving PARTIAL data for ${queryName} while fetching missing fields`, {
                cacheKey,
                fetchPolicy,
                renderPolicy: 'partial',
                availabilityStatus: availability?.status,
                timestamp: new Date().toISOString()
            });
        }
    }

    // CRITICAL: Track if we're potentially triggering Suspense
    const queryOptions = {
        fetchPolicy, // SWR ✨
        // Use appropriate render policy based on cache status
        ...(useFullRenderPolicy ? { UNSTABLE_renderPolicy: 'full' as const } : {}),
        ...(usePartialRenderPolicy ? { UNSTABLE_renderPolicy: 'partial' as const } : {})
    };
    
    // Debug: Log the actual options being used
    if (process.env.NODE_ENV === 'development') {
        const renderPolicy = useFullRenderPolicy ? 'full' : usePartialRenderPolicy ? 'partial' : 'default';
        if (fetchPolicy === 'store-only' || renderPolicy !== 'default') {
            debug.log(`🎯 [SWR] Query options for ${queryName}`, {
                fetchPolicy,
                renderPolicy,
                cacheKey,
                timestamp: new Date().toISOString(),
                willSuspend: fetchPolicy !== 'store-only' && renderPolicy === 'default'
            });
        }
    }
    
    const result = useLazyLoadQuery<T>(query, variables, queryOptions);

    // Enhanced cache hit/miss tracking with detailed analysis
    useEffect(() => {
        if (!trackingRef.current && delayComplete) {
            trackingRef.current = true;

            const loadTime = Date.now() - startTimeRef.current;
            // More sophisticated cache detection:
            // - Very fast response (< 50ms) is likely from cache
            // - Combined with debug delay (if enabled), should be much faster than 10s
            // - Also consider if we have data immediately available
            // Determine cache hit deterministically using Relay's environment.check().
            // Fallback to legacy timing heuristic only if check() throws (e.g. during testing).
            let fromCache: boolean;
            try {
                const requestNode = getRequest(query);
                const opDesc = createOperationDescriptor(requestNode, variables);
                const availability = environment.check(opDesc) as RelayAvailability;
                fromCache = availability.status === 'available' || availability.status === 'stale';
            } catch {
                // keep previous heuristic as a safety net
                fromCache = window.__DEBUG_CACHE_DELAY__ ? loadTime < 8000 : loadTime < 50;
            }

            // Detailed cache analysis
            const performCacheAnalysis = async () => {
                try {
                    const environment = await getRelayEnvironment();
                    const store = environment.getStore();
                    // PHASE 2: Wrap expensive snapshot behind NODE_ENV check for performance
                    const storeSnapshot = process.env.NODE_ENV === 'development' ? store.getSource().toJSON() : {};

                    // Analyze what records are available vs what might be needed
                    const availableRecords = Object.keys(storeSnapshot);
                    const businessRecords = availableRecords.filter((id) => {
                        const record = storeSnapshot[id] as { __typename?: string };
                        return record?.__typename && !id.startsWith('client:');
                    });

                    debug.storeAnalysis(storeSnapshot);

                    if (!fromCache) {
                        // PHASE 3 FIX: Detailed cache miss analysis based on actual TTL, not heuristics
                        const opSavedAt = (window.__RELAY_OP_SAVED_AT__ ?? {})[cacheKey];
                        const now = Date.now();
                        
                        const reason: CacheMissReason =
                            !opSavedAt
                                ? 'forced_network_due_to_missing_timestamp'
                                : now - opSavedAt > REVALIDATE_MS
                                  ? 'ttl_expired'
                                  : businessRecords.length === 0
                                    ? 'missing_query_records'
                                    : 'incomplete_data';

                        const cacheMissDetails: CacheMissDetails = {
                            queryName,
                            reason,
                            availableRecords: businessRecords.slice(0, 10), // Show first 10 for brevity
                            variables: variables,
                            storeSize: availableRecords.length,
                            timestamp: new Date().toISOString()
                        };

                        debug.cacheMiss(cacheMissDetails);

                        // NEW: detailed missing-path logging via environment.check()
                        void (async () => {
                            try {
                                const { getRelayEnvironment } = await import('./withPersistence');
                                const { createOperationDescriptor, getRequest } = await import('relay-runtime');
                                const env = await getRelayEnvironment();
                                const requestNode = getRequest(query);
                                const opDesc = createOperationDescriptor(requestNode, variables);
                                const availability = env.check(opDesc) as RelayAvailability;
                                if (availability.status === 'missing') {
                                    console.group(`🔍 Relay missing paths for ${queryName}`);
                                    const missingPaths = availability.missing ?? [];
                                    missingPaths.forEach((path, i: number) => console.log(`${i + 1}.`, path));
                                    console.groupEnd();
                                } else {
                                    console.log(`✅ Relay check(): '${queryName}' reported AVAILABLE but still fetched network`);
                                }
                            } catch (err) {
                                console.error('Relay check() debug failed:', err);
                            }
                        })();
                    } else {
                        debug.cacheHit(queryName, loadTime, businessRecords.length);
                    }
                } catch (error) {
                    debug.error('Failed to perform cache analysis:', error);
                }
            };

            void performCacheAnalysis().catch((error) => {
                debug.error('Cache analysis failed:', error);
            });

            // Enhanced completion logging
            debug.log(`✅ GraphQL query completed: ${queryName}`, {
                loadTime: `${loadTime}ms`,
                fromCache,
                resultSize: result ? JSON.stringify(result).length : 0,
                timestamp: new Date().toISOString(),
                cacheStatus: fromCache ? 'HIT' : 'MISS'
            });

            // Track with dev utils if available
            if (window.__RELAY_DEV__?.trackCacheLoad) {
                window.__RELAY_DEV__.trackCacheLoad(startTimeRef.current, fromCache, queryName);
            }

            // Track with production observability
            if (fromCache) {
                relayObservability.trackCacheHit();
            } else {
                relayObservability.trackCacheMiss();
            }

            // Only write timestamp for NETWORK responses, not cache hits
            // Cache hits should use existing timestamps for TTL checks
            if (!fromCache && !window.__RELAY_OP_SAVED_AT__?.[cacheKey]) {
                const now = Date.now();
                if (window.__RELAY_OP_SAVED_AT__) {
                    window.__RELAY_OP_SAVED_AT__[cacheKey] = now;
                    
                    if (process.env.NODE_ENV === 'development') {
                        debug.log('📝 Per-op timestamp write', {
                            cacheKey,
                            operation: queryName,
                            timestamp: new Date(now).toISOString(),
                            context: 'network_response',
                            fromCache
                        });
                    }
                    
                    // Trigger persistence to save the updated per-operation timestamp
                    // Do NOT update global cache timestamp - keep it as initial baseline
                    void (async () => {
                        try {
                            const { notifyStoreUpdated } = await import('./createPersistedStore');
                            notifyStoreUpdated();
                        } catch (error) {
                            if (process.env.NODE_ENV === 'development') {
                                debug.error('Failed to persist timestamp:', error);
                            }
                        }
                    })();
                }
            }
        }
    }, [result, queryName, delayComplete, variables, cacheKey]);

    // Background revalidation effect - runs after first render when cache is served
    useEffect(() => {
        if (
            shouldTriggerBackgroundRevalidation &&
            delayComplete &&
            !backgroundRevalidationTriggered.current &&
            fetchPolicy === 'store-only'
        ) {
            backgroundRevalidationTriggered.current = true;

            if (process.env.NODE_ENV === 'development') {
                debug.log(`🔄 [SWR] Scheduling background revalidation for ${queryName}`, {
                    cacheKey,
                    timestamp: new Date().toISOString(),
                    reason: 'TTL expired or missing timestamp'
                });
            }

            // Schedule background revalidation on next tick to avoid blocking render
            setTimeout(() => {
                if (process.env.NODE_ENV === 'development') {
                    debug.log(`🚀 [SWR] Starting background revalidation for ${queryName}`, {
                        cacheKey,
                        timestamp: new Date().toISOString()
                    });
                }
                
                triggerBackgroundRevalidation(query, variables, queryName, cacheKey).catch((error) => {
                    if (process.env.NODE_ENV === 'development') {
                        debug.error(`Background revalidation scheduling failed for ${queryName}:`, error);
                    }
                });
            }, 0);
        }
    }, [shouldTriggerBackgroundRevalidation, delayComplete, fetchPolicy, query, variables, queryName, cacheKey]);

    // Show loading state during debug delay
    if (window.__DEBUG_CACHE_DELAY__ && !delayComplete) {
        return null as T['response']; // Return null during delay to test cache loading
    }

    return result;
}

// Development utilities for cache debugging
if (process.env.NODE_ENV === 'development') {
    // Add global flag to enable/disable debug delay
    interface DebugWindow extends Window {
        __enableCacheDelay?: () => void;
        __disableCacheDelay?: () => void;
    }

    (window as DebugWindow).__enableCacheDelay = () => {
        window.__DEBUG_CACHE_DELAY__ = true;
        debug.log('🐌 Cache debug delay enabled (10s network delay). Reload page to test cache vs network loading.');
    };

    (window as DebugWindow).__disableCacheDelay = () => {
        window.__DEBUG_CACHE_DELAY__ = false;
        debug.log('⚡ Cache debug delay disabled.');
    };

    // Add SWR debugging utility
    (window as Window & { __getSWRStats?: () => object }).__getSWRStats = () => {
        const stats = {
            activeRevalidations: revalidationTimestamps.size,
            revalidationEntries: Array.from(revalidationTimestamps.entries()).map(([key, timestamp]) => ({
                cacheKey: key,
                lastRevalidated: new Date(timestamp).toISOString(),
                ageMinutes: Math.round((Date.now() - timestamp) / 60000)
            }))
        };

        console.table(stats.revalidationEntries);
        return stats;
    };

    debug.log('🛠️  Cache debugging tools available:');
    debug.log('   - window.__enableCacheDelay() - Add 10s delay to test cache vs network');
    debug.log('   - window.__disableCacheDelay() - Remove delay');
    debug.log('   - window.__RELAY_DEV__ - Full cache inspection tools');
    debug.log('   - window.__getSWRStats() - View background revalidation statistics');
    debug.log('   - window.__validateCachePerformance() - Run cache performance validation');
    debug.log('   - window.__getPerOpTimestamps() - View per-operation cache timestamps');
    debug.log('   - window.__clearPerOpTimestamps() - Clear per-operation cache timestamps');
    debug.log('   - window.__getPayloadHashes() - View payload hash tracking for duplicate detection');
    debug.log('   - window.__clearPayloadHashes() - Clear payload hash tracking');

    // Add comprehensive cache validation
    (window as Window & { __validateCachePerformance?: () => Promise<object> }).__validateCachePerformance = async () => {
        try {
            const { debug } = await import('./debug');
            const { getCacheFilterStats } = await import('./cacheFilters');
            const { getRelayEnvironment } = await import('./withPersistence');

            const environment = await getRelayEnvironment();
            const store = environment.getStore();
            // PHASE 2: Already gated behind NODE_ENV development check above
            const storeSnapshot = store.getSource().toJSON();

            // Get cache filter statistics
            const filterStats = getCacheFilterStats(storeSnapshot);

            // Count connections and pagination data
            let connectionRecords = 0;
            let businessConnections = 0;
            let paginationEdges = 0;

            for (const [dataID, record] of Object.entries(storeSnapshot)) {
                if (dataID.includes('__connection') && typeof record === 'object' && record) {
                    connectionRecords++;

                    const connectionRecord = record as {
                        edges?: Record<string, unknown>;
                    };

                    if (connectionRecord.edges) {
                        const edgeEntries = Object.entries(connectionRecord.edges);

                        // Check if this contains business entities
                        const hasBusinessEntities = edgeEntries.some(([, edgeValue]) => {
                            const edge = edgeValue as { node?: { __typename?: string } };
                            const typename = edge?.node?.__typename;
                            return typename && ['TimeSheet', 'PayStub', 'Employee', 'Employer', 'User'].includes(typename);
                        });

                        if (hasBusinessEntities) {
                            businessConnections++;
                            paginationEdges += edgeEntries.length;
                        }
                    }
                }
            }

            // Run validation
            const validationResult = debug.performanceValidation.validateCachePerformance({
                totalRecords: filterStats.totalRecords,
                persistedRecords: filterStats.persistedRecords,
                totalSize: filterStats.totalSize,
                persistedSize: filterStats.persistedSize,
                connectionRecords,
                businessConnections,
                paginationEdges
            });

            return validationResult;
        } catch (error) {
            console.error('Cache performance validation failed:', error);
            return { passed: false, error: error instanceof Error ? error.message : 'Unknown error' };
        }
    };

    // Add per-operation timestamp debugging utilities
    (window as Window & { __getPerOpTimestamps?: () => object }).__getPerOpTimestamps = () => {
        const timestamps = window.__RELAY_OP_SAVED_AT__ || {};
        const now = Date.now();

        const formattedTimestamps = Object.entries(timestamps).map(([cacheKey, timestamp]) => ({
            cacheKey,
            savedAt: new Date(timestamp).toISOString(),
            ageSeconds: Math.round((now - timestamp) / 1000),
            ageMinutes: Math.round((now - timestamp) / 60000),
            isStale: now - timestamp > 30000 // 30 second threshold
        }));

        console.group('📊 Per-Operation Cache Timestamps');
        console.table(formattedTimestamps);
        console.groupEnd();

        return {
            totalOperations: formattedTimestamps.length,
            staleOperations: formattedTimestamps.filter((op) => op.isStale).length,
            freshOperations: formattedTimestamps.filter((op) => !op.isStale).length,
            timestamps: formattedTimestamps
        };
    };

    (window as Window & { __clearPerOpTimestamps?: () => void }).__clearPerOpTimestamps = () => {
        if (window.__RELAY_OP_SAVED_AT__) {
            const opSavedAt = window.__RELAY_OP_SAVED_AT__;
            const count = Object.keys(opSavedAt).length;

            // Mutate in place to preserve references stored elsewhere
            Object.keys(opSavedAt).forEach((key) => delete opSavedAt[key]);

            // Trigger persistence to save cleared state
            void import('./createPersistedStore').then(({ notifyStoreUpdated }) => {
                notifyStoreUpdated();
            });

            debug.log(`🧹 Cleared ${count} per-operation timestamps. All queries will revalidate on next access.`);
        } else {
            debug.log('No per-operation timestamps to clear.');
        }
    };

    // Add payload hash debugging utilities
    (window as Window & { __getPayloadHashes?: () => object }).__getPayloadHashes = () => {
        const hashes = Object.entries(payloadHashes).map(([cacheKey, hash]) => ({
            cacheKey,
            hash,
            created: 'unknown' // We don't track creation time for hashes currently
        }));

        console.group('📊 Payload Hash Tracking');
        console.table(hashes);
        console.groupEnd();

        return {
            totalHashes: hashes.length,
            hashes
        };
    };

    (window as Window & { __clearPayloadHashes?: () => void }).__clearPayloadHashes = () => {
        const count = Object.keys(payloadHashes).length;

        // Clear all payload hashes
        Object.keys(payloadHashes).forEach((key) => delete payloadHashes[key]);

        // Also clear window global if it exists
        if (window.__RELAY_OP_PAYLOAD_HASHES__) {
            Object.keys(window.__RELAY_OP_PAYLOAD_HASHES__).forEach((key) => delete window.__RELAY_OP_PAYLOAD_HASHES__![key]);
        }

        debug.log(`🧹 Cleared ${count} payload hashes. Identical payload detection reset.`);
    };
}

// Export for testing
export { triggerBackgroundRevalidation };
