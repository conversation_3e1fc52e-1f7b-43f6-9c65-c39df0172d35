/**
 * Debug utility that tree-shakes in production
 * All debug output should go through this module to ensure clean production builds
 *
 * PHASE 4: Enhanced tree-shaking with production environment checks
 * - All debug functions are gated behind NODE_ENV checks
 * - Expensive operations like JSON.stringify are optimized out in production
 * - Console methods are completely removed in production builds
 */

/**
 * Stable JSON stringification utility for cache key generation
 *
 * This utility provides deterministic serialization of objects for cache key generation
 * by sorting object keys consistently. This fixes the cache-key stability issue identified
 * in the SWR investigation where identical objects could produce different cache keys.
 *
 * @param obj - Object to stringify
 * @returns Deterministic JSON string representation with sorted keys
 */
export function stableStringify(obj: unknown): string {
    try {
        return JSON.stringify(obj, (_key, value) => {
            // Sort object keys for deterministic serialization
            if (value && typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
                const sortedObj: Record<string, unknown> = {};
                const keys = Object.keys(value).sort();
                for (const key of keys) {
                    sortedObj[key] = (value as Record<string, unknown>)[key];
                }
                return sortedObj;
            }
            return value;
        });
    } catch (error) {
        if (process.env.NODE_ENV === 'development') {
            console.warn('[stableStringify] Serialization failed, using fallback:', error);
        }
        return '[stringify-error]';
    }
}

/**
 * Type-safe version for record objects (most common use case)
 */
export function stableStringifyRecord(obj: Record<string, unknown>): string {
    return stableStringify(obj);
}

/**
 * Clean and normalize variables for consistent cache key generation
 *
 * This function addresses the variable sanitization requirements from the SWR fix plan:
 * 1. Deep-clones variables to avoid mutations
 * 2. Prunes keys whose value is undefined
 * 3. Converts Date objects to ISO strings for deterministic serialization
 * 4. Ensures arrays are sorted when order doesn't matter (configurable)
 *
 * @param variables - Raw GraphQL variables object
 * @param options - Configuration options for cleaning behavior
 * @returns Cleaned and normalized variables object
 */
export function cleanVariables(
    variables: Record<string, unknown>,
    options: {
        sortArrays?: boolean;
        convertDates?: boolean;
        pruneUndefined?: boolean;
    } = {}
): Record<string, unknown> {
    const { sortArrays = true, convertDates = true, pruneUndefined = true } = options;

    function deepCleanValue(value: unknown): unknown {
        if (value === undefined) {
            return undefined; // Will be pruned later if pruneUndefined is true
        }

        if (value === null) {
            return null;
        }

        // Convert Date objects to ISO strings for deterministic serialization
        if (convertDates && value instanceof Date) {
            return value.toISOString();
        }

        // Handle functions (should never happen in GraphQL variables, but be defensive)
        if (typeof value === 'function') {
            return '[function]';
        }

        // Handle arrays
        if (Array.isArray(value)) {
            const cleanedArray = value.map(deepCleanValue);

            // Sort arrays if requested and they contain primitive values for deterministic ordering
            if (
                sortArrays &&
                cleanedArray.every(
                    (item) => typeof item === 'string' || typeof item === 'number' || typeof item === 'boolean' || item === null
                )
            ) {
                return cleanedArray.sort();
            }

            return cleanedArray;
        }

        // Handle objects
        if (typeof value === 'object' && value !== null) {
            const cleanedObj: Record<string, unknown> = {};

            for (const [key, val] of Object.entries(value)) {
                const cleanedVal = deepCleanValue(val);

                // Prune undefined values if requested
                if (pruneUndefined && cleanedVal === undefined) {
                    continue;
                }

                cleanedObj[key] = cleanedVal;
            }

            return cleanedObj;
        }

        // Return primitive values as-is
        return value;
    }

    const cleaned = deepCleanValue(variables) as Record<string, unknown>;

    if (process.env.NODE_ENV === 'development') {
        const originalSize = JSON.stringify(variables).length;
        const cleanedSize = JSON.stringify(cleaned).length;

        if (originalSize !== cleanedSize) {
            console.log('[cleanVariables] Normalized variables', {
                originalSize,
                cleanedSize,
                reduction: originalSize - cleanedSize,
                pruneUndefined,
                convertDates,
                sortArrays
            });
        }
    }

    return cleaned;
}

/**
 * Prepare variables for use with useSWRQuery to ensure cache key stability
 * This is the main function that should be called before passing variables to useSWRQuery
 */
export function prepareVariables(variables: Record<string, unknown>): Record<string, unknown> {
    return cleanVariables(variables, {
        sortArrays: true,
        convertDates: true,
        pruneUndefined: true
    });
}

// Cache miss reason types for detailed debugging
// PHASE 4: Enhanced debug labels for better clarity
export type CacheMissReason =
    | 'missing_query_records'
    | 'stale_data'
    | 'forced_network_due_to_missing_timestamp' // PHASE 4: Renamed from 'network_first_policy' for clarity
    | 'incomplete_data'
    | 'auth_error_cleared'
    | 'cache_key_mismatch'
    | 'store_hydration_failed'
    | 'variable_normalization_mismatch'
    | 'ttl_expired' // PHASE 4: New reason for TTL-based revalidation
    | 'edge_preservation_triggered' // PHASE 4: New reason for edge snapshot scenarios
    | 'unknown';

export interface CacheMissDetails {
    queryName: string;
    reason: CacheMissReason;
    missingRecords?: string[];
    availableRecords?: string[];
    variables?: Record<string, unknown>;
    storeSize?: number;
    timestamp: string;
}

export interface QueryAnalysis {
    queryName: string;
    expectedRecords: string[];
    availableRecords: string[];
    missingRecords: string[];
    cacheHitProbability: number;
    reason: CacheMissReason;
}

// Debug verbosity control - load from localStorage if available
let debugVerbosity: 'minimal' | 'normal' | 'verbose' = 'minimal';
let swrDebugMode = false;

// Load persisted debug settings
if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
    try {
        const savedVerbosity = localStorage.getItem('relay_debug_verbosity');
        if (savedVerbosity === 'minimal' || savedVerbosity === 'normal' || savedVerbosity === 'verbose') {
            debugVerbosity = savedVerbosity;
        }

        const savedSWRDebug = localStorage.getItem('relay_swr_debug');
        if (savedSWRDebug === 'true') {
            swrDebugMode = true;
            console.log(`🔍 SWR Debug Mode AUTO-ENABLED from previous session`);
            console.log(`   Use window.__disableSWRDebug() to turn off`);
        }
    } catch (e) {
        // localStorage might be blocked
    }
}

// Allow runtime control of debug verbosity
if (typeof window !== 'undefined') {
    (window as any).__setDebugVerbosity = (level: 'minimal' | 'normal' | 'verbose', persist = true) => {
        debugVerbosity = level;
        if (persist && typeof localStorage !== 'undefined') {
            try {
                localStorage.setItem('relay_debug_verbosity', level);
                console.log(`🎚️ Debug verbosity set to: ${level} (persisted)`);
            } catch (e) {
                console.log(`🎚️ Debug verbosity set to: ${level} (session only)`);
            }
        } else {
            console.log(`🎚️ Debug verbosity set to: ${level} (session only)`);
        }
    };

    (window as any).__enableSWRDebug = (persist = true) => {
        swrDebugMode = true;
        if (persist && typeof localStorage !== 'undefined') {
            try {
                localStorage.setItem('relay_swr_debug', 'true');
                console.log(`🔍 SWR Debug Mode ENABLED (will persist across reloads)`);
            } catch (e) {
                console.log(`🔍 SWR Debug Mode ENABLED (session only)`);
            }
        } else {
            console.log(`🔍 SWR Debug Mode ENABLED (session only)`);
        }
        console.log(`   Key indicators to watch:`);
        console.log(`   🍞 = Serving stale data`);
        console.log(`   🔄 = Background revalidation`);
        console.log(`   ⏰ = TTL checks`);
        console.log(`   📊 = Cache availability`);
        console.log(`   🎯 = Query options (fetchPolicy, renderPolicy)`);
    };

    (window as any).__disableSWRDebug = () => {
        swrDebugMode = false;
        if (typeof localStorage !== 'undefined') {
            try {
                localStorage.removeItem('relay_swr_debug');
                console.log(`🔍 SWR Debug Mode DISABLED (cleared from storage)`);
            } catch (e) {
                console.log(`🔍 SWR Debug Mode DISABLED`);
            }
        } else {
            console.log(`🔍 SWR Debug Mode DISABLED`);
        }
    };

    (window as any).__clearDebugSettings = () => {
        if (typeof localStorage !== 'undefined') {
            try {
                localStorage.removeItem('relay_debug_verbosity');
                localStorage.removeItem('relay_swr_debug');
                console.log(`🧹 All debug settings cleared from storage`);
            } catch (e) {
                console.log(`❌ Could not clear debug settings from storage`);
            }
        }
        debugVerbosity = 'minimal';
        swrDebugMode = false;
    };

    (window as any).__getDebugSettings = () => {
        const settings = {
            verbosity: debugVerbosity,
            swrDebugMode: swrDebugMode,
            persisted: {
                verbosity: null as string | null,
                swrDebug: null as string | null
            }
        };

        // Check what's in localStorage
        if (typeof localStorage !== 'undefined') {
            try {
                settings.persisted.verbosity = localStorage.getItem('relay_debug_verbosity');
                settings.persisted.swrDebug = localStorage.getItem('relay_swr_debug');
            } catch (e) {
                // localStorage might be blocked
            }
        }

        // Pretty print the settings
        console.log(`📊 Current Debug Settings:`);
        console.log(`   Verbosity: ${debugVerbosity}${settings.persisted.verbosity ? ' (persisted)' : ' (session only)'}`);
        console.log(
            `   SWR Debug: ${swrDebugMode ? '✅ ENABLED' : '❌ DISABLED'}${settings.persisted.swrDebug ? ' (persisted)' : swrDebugMode ? ' (session only)' : ''}`
        );

        if (swrDebugMode) {
            console.log(`   📍 Showing only SWR-related messages`);
        } else if (debugVerbosity === 'minimal') {
            console.log(`   📍 Showing minimal debug output (most noise filtered)`);
        } else if (debugVerbosity === 'normal') {
            console.log(`   📍 Showing normal debug output`);
        } else if (debugVerbosity === 'verbose') {
            console.log(`   📍 Showing all debug output (verbose mode)`);
        }

        return settings;
    };

    // Also add a short alias for convenience
    (window as any).__debug = (window as any).__getDebugSettings;

    // Add a help function
    (window as any).__debugHelp = () => {
        console.log(`🛠️ Debug Commands Available:`);
        console.log(`   __getDebugSettings() or __debug()  - Show current settings`);
        console.log(`   __enableSWRDebug()                  - Enable SWR-only mode (persisted)`);
        console.log(`   __enableSWRDebug(false)             - Enable SWR-only mode (session only)`);
        console.log(`   __disableSWRDebug()                 - Disable SWR mode`);
        console.log(`   __setDebugVerbosity('minimal')      - Minimal output (default)`);
        console.log(`   __setDebugVerbosity('normal')       - Normal output`);
        console.log(`   __setDebugVerbosity('verbose')      - All debug output`);
        console.log(`   __clearDebugSettings()              - Clear all settings`);
        console.log(`   __debugHelp()                       - Show this help`);
        console.log(``);
        console.log(`💡 Pro tip: Settings persist across page reloads by default!`);
    };

    // Show current settings on load if any are active
    if (swrDebugMode || debugVerbosity !== 'minimal') {
        console.log(`🔧 Debug settings loaded. Run __debug() to see current settings or __debugHelp() for commands.`);
    }
}

// PHASE 4: Debug functions with aggressive tree-shaking in production
export const debug = {
    log:
        process.env.NODE_ENV === 'development'
            ? (...args: unknown[]) => {
                  const msg = args[0] as string;

                  // SWR Debug Mode - show ONLY critical SWR-related messages
                  if (swrDebugMode) {
                      if (typeof msg === 'string') {
                          // Define what we WANT to see - be VERY selective
                          const criticalSWRMessages = [
                              // SWR Mode Status
                              '🔍 SWR Debug Mode AUTO-ENABLED from previous session',
                              '🔧 Debug settings loaded',

                              // Critical SWR Indicators
                              '🍞 [SWR] Serving STALE',
                              '🧩 [SWR] Serving PARTIAL',
                              '❌ [SWR] No cached data',
                              '⚠️ [SWR] Query has missing fields',

                              // TTL and Cache Status
                              '⏰ [SWR] TTL Check:',
                              '📊 [SWR] Cache availability',

                              // Query Execution
                              '🚀 Starting GraphQL query:',
                              '🎯 [SWR] Query options',

                              // Background Revalidation
                              '🔄 [SWR] Scheduling background',
                              '🚀 [SWR] Starting background',
                              '📥 [SWR] Background revalidation',
                              '✨ [SWR] Background revalidation complete'
                          ];

                          // Check if message matches any critical pattern
                          const shouldShow = criticalSWRMessages.some((pattern) => msg.includes(pattern));

                          if (!shouldShow) return;
                      }
                      console.log(...args);
                      return;
                  }

                  // Filter out noise in minimal mode
                  if (debugVerbosity === 'minimal') {
                      if (typeof msg === 'string') {
                          // Skip these noisy messages
                          if (msg.includes('🔑 [Cache Key] Generated')) return;
                          if (msg.includes('📊 Query Analysis:')) return;
                          if (msg.includes('🏪 Store Analysis')) return;
                          if (msg.includes('🛠️  Cache debugging tools')) return;
                          if (msg.includes('🔧 Enhanced Relay Dev Tools')) return;
                          if (msg.includes('Relay pre-check:')) return;
                          if (msg.includes('[Bootstrap]')) return;
                          if (msg.includes('[initRelayEnvironment]')) return;
                          if (msg.includes('[Persistence] Filtered')) return;
                          if (msg.includes('[Persistence] Persisted')) return;
                          if (msg.includes('[Relay Cache] Persisted size')) return;
                          if (msg.includes('flushNow: Immediate')) return;
                          if (msg.includes('🚀 Relay Cache Debugging Ready')) return;
                          if (msg.includes('Per-op timestamp write')) return;
                          if (msg.includes('Background revalidation completed')) return;
                          if (msg.includes('[SWR] Background fetch outcome')) return;
                          if (msg.includes('[SWR] Store diff')) return;
                          if (msg.includes('[SWR] Cache key for')) return;
                          if (msg.includes('✅ GraphQL query completed')) return;
                          if (msg.includes('⚡ Load completed')) return;
                          if (msg.includes('⚡ Cache Hit:')) return;
                          if (msg.includes('[Store Hydration]')) return;
                          if (msg.includes('[Selective Cache]')) return;
                          if (msg.includes('📅 Using cache saved time')) return;
                          if (msg.includes('💾 [Persistence]')) return;
                          if (msg.includes('📄 Pagination Analysis:')) return;
                          if (msg.includes('observability.ts')) return;
                          if (msg.includes('[HelpScout Beacon]')) return;
                          if (msg.includes('TimesheetRoster.tsx: Data loaded')) return;
                          if (msg.includes('devUtils.ts')) return;
                          if (msg.includes('Sent timestamp backup via sendBeacon')) return;

                          // ALWAYS show these critical SWR messages in minimal mode
                          if (msg.includes('🍞 [SWR] Serving STALE')) return console.log(...args);
                          if (msg.includes('🧩 [SWR] Serving PARTIAL')) return console.log(...args);
                          if (msg.includes('❌ [SWR] No cached data')) return console.log(...args);
                          if (msg.includes('⚠️ [SWR] Query has missing fields')) return console.log(...args);
                      }
                  }
                  console.log(...args);
              }
            : () => {
                  /* no-op in production */
              },

    warn:
        process.env.NODE_ENV === 'development'
            ? (...args: unknown[]) => console.warn(...args)
            : () => {
                  /* no-op in production */
              },

    error:
        process.env.NODE_ENV === 'development'
            ? (...args: unknown[]) => console.error(...args)
            : () => {
                  /* no-op in production */
              },

    table:
        process.env.NODE_ENV === 'development'
            ? (data: unknown) => {
                  // Skip tables in SWR debug mode - they're usually noise
                  if (swrDebugMode) return;
                  if (debugVerbosity !== 'minimal') {
                      console.table(data);
                  }
              }
            : () => {
                  /* no-op in production */
              },

    debug:
        process.env.NODE_ENV === 'development'
            ? (...args: unknown[]) => {
                  if (debugVerbosity === 'verbose') {
                      console.debug(...args);
                  }
              }
            : () => {
                  /* no-op in production */
              },

    // PHASE 4: Enhanced cache debugging methods with tree-shaking
    cacheMiss:
        process.env.NODE_ENV === 'development'
            ? (details: CacheMissDetails) => {
                  // Skip cache miss analysis in SWR debug mode - it's noise
                  if (swrDebugMode) return;
                  console.group(`🔍 Cache Miss Analysis: ${details.queryName}`);
                  console.log(`📋 Reason: ${details.reason}`);
                  if (details.missingRecords?.length) {
                      console.log(`❌ Missing Records (${details.missingRecords.length}):`, details.missingRecords);
                  }
                  if (details.availableRecords?.length) {
                      console.log(`✅ Available Records (${details.availableRecords.length}):`, details.availableRecords);
                  }
                  if (details.variables) {
                      console.log(`🔧 Variables:`, details.variables);
                  }
                  if (details.storeSize) {
                      console.log(`📦 Store Size: ${details.storeSize} records`);
                  }
                  console.log(`⏰ Timestamp: ${details.timestamp}`);
                  console.groupEnd();
              }
            : () => {
                  /* no-op in production */
              },

    cacheHit:
        process.env.NODE_ENV === 'development'
            ? (queryName: string, loadTime: number, recordCount?: number) => {
                  // Skip cache hit messages in SWR debug mode - they're noise
                  if (swrDebugMode) return;
                  console.log(`⚡ Cache Hit: ${queryName} loaded in ${loadTime}ms${recordCount ? ` (${recordCount} records)` : ''}`);
              }
            : () => {
                  /* no-op in production */
              },

    queryAnalysis: (analysis: QueryAnalysis) => {
        if (process.env.NODE_ENV === 'development') {
            // Skip query analysis in SWR debug mode - it's noise
            if (swrDebugMode) return;
            console.group(`📊 Query Analysis: ${analysis.queryName}`);
            console.log(`🎯 Cache Hit Probability: ${(analysis.cacheHitProbability * 100).toFixed(1)}%`);
            console.log(`📝 Expected Records: ${analysis.expectedRecords.length}`);
            console.log(`✅ Available Records: ${analysis.availableRecords.length}`);
            console.log(`❌ Missing Records: ${analysis.missingRecords.length}`);
            if (analysis.missingRecords.length > 0) {
                console.log(`🔍 Missing:`, analysis.missingRecords);
            }
            console.log(`📋 Predicted Reason: ${analysis.reason}`);
            console.groupEnd();
        }
    },

    storeAnalysis: (storeSnapshot: Record<string, unknown>) => {
        if (process.env.NODE_ENV === 'development') {
            // Skip store analysis in SWR debug mode - it's noise
            if (swrDebugMode) return;
            const recordTypes = new Map<string, number>();
            const totalRecords = Object.keys(storeSnapshot).length;

            for (const [, record] of Object.entries(storeSnapshot)) {
                const typedRecord = record as { __typename?: string };
                const typename = typedRecord?.__typename || 'Unknown';
                recordTypes.set(typename, (recordTypes.get(typename) || 0) + 1);
            }

            console.group(`🏪 Store Analysis (${totalRecords} total records)`);
            console.table(Object.fromEntries(recordTypes));
            console.groupEnd();
        }
    },

    networkDelay: (queryName: string, delayMs: number) => {
        if (process.env.NODE_ENV === 'development') {
            console.log(`⏳ Network Delay Applied: ${queryName} delayed by ${delayMs}ms for cache testing`);
        }
    },

    persistence:
        process.env.NODE_ENV === 'development'
            ? (message: string, data?: unknown) => {
                  // Skip persistence messages in SWR debug mode - they're noise
                  if (swrDebugMode) return;
                  if (data) {
                      console.log(`💾 [Persistence] ${message}`, data);
                  } else {
                      console.log(`💾 [Persistence] ${message}`);
                  }
              }
            : () => {
                  /* no-op in production */
              },

    // Enhanced cache key debugging
    cacheKeyMismatch: (details: {
        queryName: string;
        persistedKeys: string[];
        lookupKeys: string[];
        variables: Record<string, unknown>;
        timestamp: string;
    }) => {
        if (process.env.NODE_ENV === 'development') {
            console.group(`🔑 Cache Key Mismatch Analysis: ${details.queryName}`);
            console.log(`📋 Persisted Keys (${details.persistedKeys.length}):`, details.persistedKeys);
            console.log(`🔍 Lookup Keys (${details.lookupKeys.length}):`, details.lookupKeys);
            console.log(`🔧 Variables:`, details.variables);
            console.log(`⏰ Timestamp: ${details.timestamp}`);

            // Find differences
            const persistedSet = new Set(details.persistedKeys);
            const lookupSet = new Set(details.lookupKeys);
            const onlyInPersisted = details.persistedKeys.filter((key) => !lookupSet.has(key));
            const onlyInLookup = details.lookupKeys.filter((key) => !persistedSet.has(key));

            if (onlyInPersisted.length > 0) {
                console.log(`❌ Keys only in persisted cache (${onlyInPersisted.length}):`, onlyInPersisted);
            }
            if (onlyInLookup.length > 0) {
                console.log(`❌ Keys only in lookup (${onlyInLookup.length}):`, onlyInLookup);
            }

            const intersection = details.persistedKeys.filter((key) => lookupSet.has(key));
            if (intersection.length > 0) {
                console.log(`✅ Matching Keys (${intersection.length}):`, intersection);
            }

            console.groupEnd();
        }
    },

    storeHydration: (details: {
        step: 'start' | 'indexeddb_read' | 'store_restore' | 'complete' | 'error' | 'cache_age_set' | 'per_op_timestamps_exposed';
        message: string;
        data?: unknown;
        recordCount?: number;
        duration?: number;
        connectionRecords?: number;
        savedAt?: string;
        hydratedAt?: string;
        sampleKeys?: string[];
        paginationSlices?: {
            businessConnections: number;
            totalEdges: number;
            preservedPageInfo: number;
        };
    }) => {
        if (process.env.NODE_ENV === 'development') {
            // Skip store hydration messages in SWR debug mode - they're noise
            if (swrDebugMode) return;
            const stepEmoji = {
                start: '🚀',
                indexeddb_read: '📖',
                store_restore: '🔄',
                complete: '✅',
                error: '❌',
                cache_age_set: '⏰',
                per_op_timestamps_exposed: '🔑'
            };

            let message = `${stepEmoji[details.step]} [Store Hydration] ${details.message}`;

            if (details.recordCount) {
                message += ` (${details.recordCount} records)`;
            }

            if (details.connectionRecords) {
                message += ` [${details.connectionRecords} connections]`;
            }

            if (details.duration) {
                message += ` in ${details.duration}ms`;
            }

            console.log(message, details.data || '');

            // Enhanced pagination slice reporting
            if (details.paginationSlices) {
                console.log(`📄 Pagination Analysis:`, {
                    businessConnections: details.paginationSlices.businessConnections,
                    totalEdges: details.paginationSlices.totalEdges,
                    preservedPageInfo: details.paginationSlices.preservedPageInfo,
                    averageEdgesPerConnection:
                        details.paginationSlices.businessConnections > 0
                            ? Math.round(details.paginationSlices.totalEdges / details.paginationSlices.businessConnections)
                            : 0
                });
            }
        }
    },

    // Performance validation utilities
    performanceValidation: {
        validateCachePerformance: (stats: {
            totalRecords: number;
            persistedRecords: number;
            totalSize: number;
            persistedSize: number;
            connectionRecords: number;
            businessConnections: number;
            paginationEdges: number;
        }) => {
            if (process.env.NODE_ENV === 'development') {
                const persistenceRate = stats.persistedRecords / stats.totalRecords;
                const sizeReduction = (stats.totalSize - stats.persistedSize) / stats.totalSize;

                console.group('📊 Cache Performance Validation');
                console.log(
                    `✅ Persistence Rate: ${(persistenceRate * 100).toFixed(1)}% (${stats.persistedRecords}/${stats.totalRecords})`
                );
                console.log(`📦 Size Reduction: ${(sizeReduction * 100).toFixed(1)}% (${Math.round(stats.persistedSize / 1024)}KB saved)`);
                console.log(`🔗 Connection Preservation: ${stats.connectionRecords} connections, ${stats.businessConnections} business`);
                console.log(`📄 Pagination Edges: ${stats.paginationEdges} edges preserved`);

                // Validation checks
                const validationResults = [];

                if (persistenceRate >= 0.9) {
                    validationResults.push('✅ Persistence rate meets >90% requirement');
                } else {
                    validationResults.push(`❌ Persistence rate ${(persistenceRate * 100).toFixed(1)}% below 90% target`);
                }

                if (stats.businessConnections > 0 && stats.paginationEdges > 0) {
                    validationResults.push('✅ Pagination slices are being preserved');
                } else if (stats.businessConnections > 0) {
                    validationResults.push('⚠️ Business connections found but no pagination edges preserved');
                }

                if (sizeReduction > 0) {
                    validationResults.push(`✅ Cache filtering providing ${(sizeReduction * 100).toFixed(1)}% size reduction`);
                }

                validationResults.forEach((result) => console.log(result));
                console.groupEnd();

                return {
                    passed: persistenceRate >= 0.9 && (stats.businessConnections === 0 || stats.paginationEdges > 0),
                    persistenceRate,
                    sizeReduction,
                    validationResults
                };
            }

            return { passed: true, persistenceRate: 1, sizeReduction: 0, validationResults: [] };
        },

        validateSWRBehavior: (swrStats: {
            totalQueries: number;
            cacheHits: number;
            backgroundRevalidations: number;
            averageLoadTime: number;
            staleServed: number;
        }) => {
            if (process.env.NODE_ENV === 'development') {
                const cacheHitRate = swrStats.cacheHits / swrStats.totalQueries;
                const revalidationRate = swrStats.backgroundRevalidations / swrStats.totalQueries;

                console.group('🔄 SWR Behavior Validation');
                console.log(`⚡ Cache Hit Rate: ${(cacheHitRate * 100).toFixed(1)}% (${swrStats.cacheHits}/${swrStats.totalQueries})`);
                console.log(`🔄 Background Revalidation Rate: ${(revalidationRate * 100).toFixed(1)}%`);
                console.log(`📊 Average Load Time: ${swrStats.averageLoadTime.toFixed(1)}ms`);
                console.log(`📤 Stale Content Served: ${swrStats.staleServed} times`);

                const validationResults = [];

                if (cacheHitRate >= 0.7) {
                    validationResults.push(`✅ Cache hit rate ${(cacheHitRate * 100).toFixed(1)}% meets >70% target`);
                } else {
                    validationResults.push(`❌ Cache hit rate ${(cacheHitRate * 100).toFixed(1)}% below 70% target`);
                }

                if (swrStats.averageLoadTime < 100) {
                    validationResults.push(`✅ Average load time ${swrStats.averageLoadTime.toFixed(1)}ms is optimal`);
                } else if (swrStats.averageLoadTime < 500) {
                    validationResults.push(`⚠️ Average load time ${swrStats.averageLoadTime.toFixed(1)}ms is acceptable`);
                } else {
                    validationResults.push(`❌ Average load time ${swrStats.averageLoadTime.toFixed(1)}ms is too slow`);
                }

                if (swrStats.backgroundRevalidations > 0) {
                    validationResults.push('✅ Background revalidation is working');
                } else {
                    validationResults.push('⚠️ No background revalidations detected');
                }

                validationResults.forEach((result) => console.log(result));
                console.groupEnd();

                return {
                    passed: cacheHitRate >= 0.7 && swrStats.averageLoadTime < 500,
                    cacheHitRate,
                    averageLoadTime: swrStats.averageLoadTime,
                    validationResults
                };
            }

            return { passed: true, cacheHitRate: 1, averageLoadTime: 0, validationResults: [] };
        }
    },

    // PHASE 4: Enhanced SWR debugging utilities
    swrBehavior: {
        logTTLCheck: (details: {
            cacheKey: string;
            queryName: string;
            savedAt?: number;
            currentTime: number;
            ttlMs: number;
            isCacheFresh: boolean;
            willTriggerRevalidation: boolean;
        }) => {
            if (process.env.NODE_ENV === 'development') {
                const ageMs = details.savedAt ? details.currentTime - details.savedAt : undefined;
                console.group(`⏰ [SWR] TTL Check: ${details.queryName}`);
                console.log(`🔑 Cache Key: ${details.cacheKey}`);
                console.log(`📅 Saved At: ${details.savedAt ? new Date(details.savedAt).toISOString() : 'not set'}`);
                console.log(`🕐 Current Time: ${new Date(details.currentTime).toISOString()}`);
                console.log(`⏱️ Age: ${ageMs ? `${Math.round(ageMs / 1000)}s` : 'unknown'}`);
                console.log(`🎯 TTL: ${Math.round(details.ttlMs / 1000)}s`);
                console.log(`✅ Is Fresh: ${details.isCacheFresh}`);
                console.log(`🔄 Will Revalidate: ${details.willTriggerRevalidation}`);
                console.groupEnd();
            }
        },

        logEdgeSnapshot: (details: { cacheKey: string; fieldName: string; edgeCount: number; success: boolean; error?: unknown }) => {
            if (process.env.NODE_ENV === 'development') {
                const emoji = details.success ? '📸' : '❌';
                console.log(`${emoji} [SWR] Edge snapshot ${details.fieldName}:`, {
                    cacheKey: details.cacheKey,
                    edgeCount: details.edgeCount,
                    success: details.success,
                    error: details.error
                });
            }
        },

        logConnectionDiscovery: (details: { explicitFields: string[]; discoveredFields: string[]; totalFields: string[] }) => {
            if (process.env.NODE_ENV === 'development') {
                console.group(`🔍 [SWR] Connection Discovery`);
                console.log(`📋 Explicit Fields:`, details.explicitFields);
                console.log(`🆕 Discovered Fields:`, details.discoveredFields);
                console.log(`📊 Total Fields:`, details.totalFields);
                console.groupEnd();
            }
        },

        logHashComparison: (details: {
            cacheKey: string;
            fieldName: string;
            prevHash?: string;
            incomingHash: string;
            dataChanged: boolean;
            action: 'merge' | 'replace';
            existingEdgeCount: number;
            newEdgeCount: number;
        }) => {
            if (process.env.NODE_ENV === 'development') {
                const emoji = details.dataChanged ? '🔄' : '♻️';
                console.log(`${emoji} [SWR] Hash comparison ${details.fieldName}:`, {
                    cacheKey: details.cacheKey,
                    prevHash: details.prevHash || 'none',
                    incomingHash: details.incomingHash,
                    dataChanged: details.dataChanged,
                    action: details.action,
                    edges: `${details.existingEdgeCount} → ${details.newEdgeCount}`
                });
            }
        },

        logPersistenceEvent: (details: {
            event: 'immediate_flush' | 'debounced_save' | 'beforeunload' | 'sendbeacon_fallback';
            cacheKey?: string;
            success: boolean;
            error?: unknown;
            duration?: number;
        }) => {
            if (process.env.NODE_ENV === 'development') {
                const emoji = details.success ? '💾' : '❌';
                console.log(`${emoji} [SWR] Persistence ${details.event}:`, {
                    cacheKey: details.cacheKey,
                    success: details.success,
                    duration: details.duration,
                    error: details.error
                });
            }
        }
    }
};
