# SWR Debug Guide - Fixing the Loading Spinner Issue

## Quick Debug Setup

Run these commands in the browser console:

```javascript
// Enable focused SWR debugging (persists across reloads)
window.__enableSWRDebug()

// Enable for current session only
window.__enableSWRDebug(false)

// Disable and clear from storage
window.__disableSWRDebug()

// Set verbosity level (persists by default)
window.__setDebugVerbosity('minimal')   // Default - less noise
window.__setDebugVerbosity('normal')    // More details
window.__setDebugVerbosity('verbose')   // Everything

// Clear all debug settings
window.__clearDebugSettings()
```

**Note**: Debug settings are now persisted in localStorage and will survive page reloads!

## Key Messages to Watch For

When cache is stale (>30 seconds old), you should see:

```
📊 [SWR] Cache availability: status='available'
⏰ [TTL Check] Age: 227s, TTL: 30s, Is Fresh: false
🚀 Starting GraphQL query: fetchPolicy='store-only'
🍞 [SWR] Serving STALE data while revalidating in background
🎯 [SWR] Query options: fetchPolicy='store-only', renderPolicy='full', willSuspend=false
🔄 [SWR] Scheduling background revalidation
```

## The Problem

Based on your logs, the system IS correctly:
1. ✅ Detecting stale cache (Age: 227s > TTL: 30s)
2. ✅ Setting fetchPolicy='store-only'
3. ✅ Setting renderPolicy='full'
4. ✅ Scheduling background revalidation

But you still see a loading spinner! 

## Possible Causes

### 1. Multiple Component Renders
Your logs show the query running 6 times rapidly. One of these might not have the right policy.

### 2. Suspense Boundary Issue
Even with `renderPolicy: 'full'`, React Suspense might still trigger if:
- The component unmounts/remounts
- A parent Suspense boundary is triggered
- The query variables change slightly between renders

### 3. Race Condition
The background revalidation might be updating the store before the UI renders, causing a re-suspension.

## Debug Steps

### Step 1: Check for Suspense Triggers
Look for this new debug message:
```
🎯 [SWR] Query options: willSuspend=true
```
If ANY of the 6 queries has `willSuspend=true`, that's your problem.

### Step 2: Track Component Lifecycle
Add this to TimesheetRoster.tsx:
```javascript
useEffect(() => {
    console.log('🔄 Component mounted/rendered', {
        timestamp: new Date().toISOString(),
        employerGuid
    });
    return () => {
        console.log('🔄 Component unmounting');
    };
}, []);
```

### Step 3: Check Variables Stability
The query is being called 6 times. Check if variables are changing:
```javascript
🔑 [SWR] Cache key for TimesheetRosterQuery
```
Look for different cache keys - that means variables are changing.

### Step 4: Disable Background Revalidation
Temporarily disable background revalidation to see if that's causing the issue:
```javascript
// In useSWRQuery.ts, comment out line ~1048
// backgroundRevalidationTriggered.current = true;
```

## Common Issues and Fixes

### Issue: "Still seeing spinner after fixes"
**Check**: Are ALL 6 query calls using `store-only`?
**Fix**: Ensure component isn't re-rendering with different props

### Issue: "Works on second load but not first"
**Check**: Is there data in cache? Check `availability.status`
**Fix**: The first load might have `status='missing'` 

### Issue: "Flickers between data and spinner"
**Check**: Background revalidation timing
**Fix**: The store update might be triggering re-suspension

## Critical Debug Points

1. **Count the queries**: Why is it running 6 times?
2. **Check each fetchPolicy**: Are all 6 using 'store-only'?
3. **Monitor Suspense**: Is a parent boundary catching?
4. **Track store updates**: When does the spinner appear relative to store updates?

## Next Steps

1. Enable SWR debug mode: `window.__enableSWRDebug()`
2. Reload the page
3. Look for any query with `willSuspend=true`
4. Check if all 6 queries have `renderPolicy='full'`
5. Report back with:
   - Any `willSuspend=true` messages
   - The timing of when the spinner appears
   - Whether it's all 6 queries or just some

## Emergency Workaround

If you need it working immediately, try:
```javascript
// Force all queries to never suspend
const result = useLazyLoadQuery<T>(query, variables, {
    fetchPolicy: 'store-only',
    UNSTABLE_renderPolicy: 'full'
});
```
Remove the conditional logic and always use these options.